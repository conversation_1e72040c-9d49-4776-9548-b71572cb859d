You are working with a React Router v7 project that uses the following technology stack:
- **Build tool**: Vite
- **Icons**: lucide-react
- **Data fetching**: SWR
- **State management**: Zustand  
- **UI components**: Radix UI
- **Styling**: Tailwind CSS
- **SSR**: Disabled (set to false)


# 用户-角色-权限管理模块前端页面

models：
```python
from sqlmodel import SQLModel, Field, Relationship

class User(SQLModel, table=True):
    is_active: bool = True
    is_superuser: bool = False
    username: str = Field(max_length=255)
    real_name: str | None = Field(default=None, max_length=255)
    lark_open_id: str | None = Field(default=None, max_length=255)
    hashed_password: str | None = Field(default=None, max_length=255)
    roles: list[Role] = Relationship(back_populates="users", link_model=UserRoleLink)


class Role(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    name: str
    description: str
    permissions: str = Field(description="comma seperated str(perm), e.g. api:service:module:action,api:service2:module2:action")
    users: list["User"] = Relationship(back_populates="roles", link_model=UserRoleLink)


class UserRoleLink(SQLModel, table=True):
    user_id: int | None = Field(default=None, foreign_key="user.id", primary_key=True)
    role_id: int | None = Field(default=None, foreign_key="role.id", primary_key=True)
    
    
class Perm(SQLModel):
    type: Literal["api", "field", "record"]
    service: str
    module: str
    action: Literal["read", "write", "write.create", "write.update", "write.delete"]

    name: str | None = None
    description: str | None = None

    def __str__(self):
        return f"{self.type}:{self.service}:{self.module}:{self.action}"

    def __eq__(self, other):
        return str(self) == str(other)

    def __hash__(self):
        return hash(str(self))

    @staticmethod
    def parse(value):
        if isinstance(value, Perm):
            return value

        if not isinstance(value, str):
            raise ValueError(f"Invalid permission: {value}")

        type, service, module, action = value.split(":")

        return Perm(
            type=type.strip(),
            service=service.strip(),
            module=module.strip(),
            action=action.strip(),
        )

```