{"name": "portal_dictionary", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/fs-routes": "^7.8.0", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "i": "^0.3.7", "immer": "^10.1.1", "isbot": "^5.1.27", "lodash": "^4.17.21", "lucide-react": "^0.539.0", "npm": "^11.5.2", "radix-ui": "^1.4.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "swr": "^2.3.6", "zustand": "^5.0.7"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}