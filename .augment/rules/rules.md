You are working with a React Router v7 project that uses the following technology stack:
- **Build tool**: Vite
- **Icons**: lucide-react
- **Data fetching**: SWR
- **State management**: Zustand  
- **UI components**: Radix UI
- **Styling**: Tailwind CSS
- **SSR**: Disabled (set to false)

Your task is to create a comprehensive user-role-permission management module for the frontend that interfaces with the backend models shown below.

## Backend Models Reference
The backend uses SQLModel with the following structure:

```python
from sqlmodel import SQLModel, Field, Relationship

class User(SQLModel, table=True):
    is_active: bool = True
    is_superuser: bool = False
    username: str = Field(max_length=255)
    real_name: str | None = Field(default=None, max_length=255)
    lark_open_id: str | None = Field(default=None, max_length=255)
    hashed_password: str | None = Field(default=None, max_length=255)
    roles: list[Role] = Relationship(back_populates="users", link_model=UserRoleLink)


class Role(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    name: str
    description: str
    permissions: str = Field(description="comma seperated str(perm), e.g. api:service:module:action,api:service2:module2:action")
    users: list["User"] = Relationship(back_populates="roles", link_model=UserRoleLink)


class UserRoleLink(SQLModel, table=True):
    user_id: int | None = Field(default=None, foreign_key="user.id", primary_key=True)
    role_id: int | None = Field(default=None, foreign_key="role.id", primary_key=True)
    
    
class Perm(SQLModel):
    type: Literal["api", "field", "record"]
    service: str
    module: str
    action: Literal["read", "write", "write.create", "write.update", "write.delete"]

    name: str | None = None
    description: str | None = None

    def __str__(self):
        return f"{self.type}:{self.service}:{self.module}:{self.action}"

    def __eq__(self, other):
        return str(self) == str(other)

    def __hash__(self):
        return hash(str(self))

    @staticmethod
    def parse(value):
        if isinstance(value, Perm):
            return value

        if not isinstance(value, str):
            raise ValueError(f"Invalid permission: {value}")

        type, service, module, action = value.split(":")

        return Perm(
            type=type.strip(),
            service=service.strip(),
            module=module.strip(),
            action=action.strip(),
        )
```

## Requirements

Create a complete user-role-permission management system that includes:

### 1. User Management
- List all users with pagination and search functionality
- Create new users (username, real_name, is_active, is_superuser flags)
- Edit existing user details
- Assign/unassign roles to users
- Toggle user active status
- Delete users (with confirmation)

### 2. Role Management
- List all roles with their descriptions and permission counts
- Create new roles with name, description, and permission selection
- Edit existing roles (name, description, permissions)
- View which users are assigned to each role
- Delete roles (with validation that no users are assigned)

### 3. Permission Management
- Display permissions in a structured format based on the `type:service:module:action` pattern
- Group permissions by service and module for better organization
- Provide a permission picker/selector component for role assignment
- Show permission descriptions where available

### 4. Technical Requirements
- Use TypeScript for all components and type definitions
- Create proper TypeScript interfaces that match the backend models
- Implement proper error handling and loading states
- Use SWR for data fetching with appropriate cache invalidation
- Use Zustand for any local state management needs
- Follow React Router v7 patterns for routing and navigation
- Use Radix UI components for forms, dialogs, tables, and other UI elements
- Style with Tailwind CSS following modern design patterns
- Use lucide-react icons consistently throughout the interface

### 5. User Experience
- Implement responsive design that works on desktop and mobile
- Provide clear feedback for all user actions (success/error messages)
- Include confirmation dialogs for destructive actions
- Use loading skeletons or spinners during data operations
- Implement proper form validation with helpful error messages

### 6. File Organization
- Create a dedicated module/folder structure for the user-role-permission system
- Separate components, hooks, types, and utilities appropriately
- Follow the existing project's naming conventions and folder structure